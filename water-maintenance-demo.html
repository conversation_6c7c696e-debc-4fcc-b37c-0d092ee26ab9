<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水质维护监控 - 新布局演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, rgba(2, 28, 51, 0.95) 0%, rgba(15, 45, 75, 0.9) 100%);
            min-height: 100vh;
            color: #ffffff;
            padding: 20px;
        }

        .water-maintenance {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 页面标题栏 */
        .page-header {
            margin-bottom: 24px;
        }

        .header-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: linear-gradient(135deg, rgba(40, 138, 231, 0.2) 0%, rgba(44, 145, 238, 0.1) 100%);
            border-radius: 12px;
            border: 1px solid rgba(40, 138, 231, 0.3);
            backdrop-filter: blur(10px);
        }

        .header-title h2 {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .refresh-btn {
            background: linear-gradient(135deg, #288ae7 0%, #2c91ee 100%);
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(40, 138, 231, 0.3);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(40, 138, 231, 0.4);
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            gap: 24px;
            margin-bottom: 24px;
        }

        .left-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .right-section {
            width: 400px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 通用区块样式 */
        .section-header h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
            color: #2c91ee;
            display: flex;
            align-items: center;
        }

        .section-header h3::before {
            content: '';
            width: 4px;
            height: 18px;
            background: linear-gradient(135deg, #288ae7 0%, #2c91ee 100%);
            border-radius: 2px;
            margin-right: 12px;
        }

        /* 监测指标区域 */
        .metrics-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .metric-card {
            background: linear-gradient(135deg, rgba(40, 138, 231, 0.15) 0%, rgba(44, 145, 238, 0.1) 100%);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(40, 138, 231, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #288ae7 0%, #2c91ee 100%);
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(40, 138, 231, 0.3);
            border-color: rgba(40, 138, 231, 0.4);
        }

        .metric-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            margin-bottom: 12px;
        }

        .metric-content {
            display: flex;
            align-items: baseline;
            gap: 8px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #00a0e9;
            text-shadow: 0 2px 4px rgba(0, 160, 233, 0.3);
        }

        .metric-unit {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        /* 设备状态区域 */
        .device-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .device-content {
            display: flex;
            gap: 24px;
        }

        .device-diagram {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            min-height: 200px;
        }

        .device-list {
            width: 200px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .device-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .device-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .device-number {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #2b4f63 0%, #3a5f73 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .device-name {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
        }

        .device-online {
            background: linear-gradient(135deg, #0493d6 0%, #288ae7 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(4, 147, 214, 0.3);
        }

        .device-online:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(4, 147, 214, 0.4);
        }

        .device-offline {
            background: linear-gradient(135deg, #f56c6c 0%, #e74c3c 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
        }

        /* 分析区域 */
        .analysis-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            flex: 1;
        }

        .process-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #288ae7;
        }

        .process-number {
            color: #2c91ee;
            font-weight: 600;
            min-width: 20px;
        }

        /* 图表区域 */
        .charts-section {
            display: flex;
            gap: 24px;
        }

        .chart-container {
            flex: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: 16px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-content {
            height: 300px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.5);
        }

        .time-badge {
            padding: 6px 16px;
            background: linear-gradient(135deg, #288ae7 0%, #2c91ee 100%);
            border-radius: 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(40, 138, 231, 0.3);
        }
    </style>
</head>
<body>
    <div class="water-maintenance">
        <!-- 页面标题栏 -->
        <div class="page-header">
            <div class="header-title">
                <h2>水质维护监控</h2>
                <button class="refresh-btn">🔄 刷新数据</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧区域 -->
            <div class="left-section">
                <!-- 监测指标卡片 -->
                <div class="metrics-section">
                    <div class="section-header">
                        <h3>实时监测指标</h3>
                    </div>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-name">水温</div>
                            <div class="metric-content">
                                <span class="metric-value">23.5</span>
                                <span class="metric-unit">°C</span>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">pH值</div>
                            <div class="metric-content">
                                <span class="metric-value">7.2</span>
                                <span class="metric-unit">pH</span>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">溶解氧</div>
                            <div class="metric-content">
                                <span class="metric-value">8.5</span>
                                <span class="metric-unit">mg/L</span>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">浊度</div>
                            <div class="metric-content">
                                <span class="metric-value">2.1</span>
                                <span class="metric-unit">NTU</span>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">余氯</div>
                            <div class="metric-content">
                                <span class="metric-value">0.8</span>
                                <span class="metric-unit">mg/L</span>
                            </div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-name">电导率</div>
                            <div class="metric-content">
                                <span class="metric-value">450</span>
                                <span class="metric-unit">μS/cm</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备状态监控 -->
                <div class="device-section">
                    <div class="section-header">
                        <h3>设备状态监控</h3>
                    </div>
                    <div class="device-content">
                        <div class="device-diagram">
                            <div style="color: rgba(255,255,255,0.5);">设备示意图</div>
                        </div>
                        <div class="device-list">
                            <div class="device-item">
                                <div class="device-number">1</div>
                                <div class="device-name device-online">进水泵</div>
                            </div>
                            <div class="device-item">
                                <div class="device-number">2</div>
                                <div class="device-name device-online">混凝池</div>
                            </div>
                            <div class="device-item">
                                <div class="device-number">3</div>
                                <div class="device-name device-offline">沉淀池</div>
                            </div>
                            <div class="device-item">
                                <div class="device-number">4</div>
                                <div class="device-name device-online">过滤器</div>
                            </div>
                            <div class="device-item">
                                <div class="device-number">5</div>
                                <div class="device-name device-online">消毒设备</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧区域 -->
            <div class="right-section">
                <!-- 分析过程 -->
                <div class="analysis-section">
                    <div class="section-header">
                        <h3>分析过程</h3>
                    </div>
                    <div class="process-item">
                        <span class="process-number">1.</span>
                        <span>检测水质参数是否在正常范围内...</span>
                    </div>
                    <div class="process-item">
                        <span class="process-number">2.</span>
                        <span>分析设备运行状态和效率...</span>
                    </div>
                    <div class="process-item">
                        <span class="process-number">3.</span>
                        <span>评估水质处理效果...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-container">
                <div class="chart-header">
                    <h3>历史趋势分析</h3>
                </div>
                <div class="chart-content">
                    图表区域 - 历史数据趋势
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-header">
                    <h3>24小时加药量监控</h3>
                    <div class="time-badge">24h</div>
                </div>
                <div class="chart-content">
                    图表区域 - 加药量监控
                </div>
            </div>
        </div>
    </div>
</body>
</html>
