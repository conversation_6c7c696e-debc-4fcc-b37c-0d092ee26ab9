<template>
  <div class="water-maintenance">
    <!-- 页面标题栏 -->
    <div class="page-header">
      <div class="header-title">
        <h2>水质维护监控</h2>
        <div class="header-actions">
          <el-button type="primary" icon="Refresh" @click="refreshData()">刷新数据</el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 监测指标卡片 -->
        <div class="metrics-section">
          <div class="section-header">
            <h3>实时监测指标</h3>
          </div>
          <div class="metrics-grid" v-if="waterMaintenanceData.length > 0">
            <div class="metric-card" v-for="(item, index) in waterMaintenanceData" :key="index" @click="getpointdata(item)">
              <div class="metric-header">
                <span class="metric-name">{{ item.name !== null ? item.name : '--' }}</span>
              </div>
              <div class="metric-content">
                <span class="metric-value">{{ item.value !== null ? item.value : '--' }}</span>
                <span class="metric-unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
          <div class="empty-state" v-else>
            <EmptyData :imageSrc="emptyImagePath" />
          </div>
        </div>

        <!-- 设备状态监控 -->
        <div class="device-section">
          <div class="section-header">
            <h3>设备状态监控</h3>
          </div>
          <div class="device-content">
            <div class="device-diagram">
              <img :src="waterMaintenanceImg" alt="设备示意图" />
            </div>
            <div class="device-list">
              <div class="device-item" v-for="(item, index) in equipmentLocationData" :key="index">
                <div class="device-number">{{ item.order }}</div>
                <div
                  :class="['device-name', item.status === 1 ? 'device-offline' : 'device-online']"
                  @click="changeDeviceInformation(item, index)"
                >
                  {{ item.name }}
                </div>
                <div v-if="selectedIndex === index" class="device-selected">
                  <img :src="hand" alt="选中" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 分析过程 -->
        <div class="analysis-section">
          <div class="analysis-content" v-if="processList.length > 0">
            <div class="analysis-process">
              <div class="section-header">
                <h3>分析过程</h3>
              </div>
              <div class="process-list">
                <div class="process-item" v-for="(item, index) in displayedProcesses" :key="index">
                  <span class="process-number">{{ index + 1 }}.</span>
                  <TypewriterText :key="item.id" :text="item.process" @done="handleProcessDone">
                    <span class="process-status">
                      <el-icon :color="item.status === 1 ? '#e70f0f' : '#288ae7'">
                        <Select v-if="item.status === 0" />
                        <CloseBold v-else-if="item.status === 1" />
                      </el-icon>
                    </span>
                  </TypewriterText>
                </div>
              </div>
            </div>

            <div class="dependency-indicators">
              <div class="section-header">
                <h3>依赖指标</h3>
              </div>
              <div class="indicator-list">
                <div class="indicator-item" v-for="(item, index) in relationList" :key="index">
                  <span class="indicator-number">{{ index + 1 }}.</span>
                  <span class="indicator-text">{{ item.name !== null ? item.name : '' }}：{{ item.value !== null ? item.value : '' }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="analysis-empty" v-else>
            <img :src="emptyImagePath" alt="暂无数据" />
          </div>
        </div>

        <!-- 结论与建议 -->
        <div class="conclusion-section" v-if="allProcessesDone">
          <div class="conclusion-content">
            <div class="conclusion-item">
              <div class="section-header">
                <h3>分析结论</h3>
              </div>
              <div class="conclusion-list">
                <div class="conclusion-text" v-for="(item, index) in conclusionList" :key="index">
                  {{ index + 1 }}.{{ item }}
                </div>
              </div>
            </div>

            <div class="suggestion-item">
              <div class="section-header">
                <h3>优化建议</h3>
              </div>
              <div class="suggestion-list">
                <div class="suggestion-text" v-for="(item, index) in suggestListList" :key="index">
                  {{ index + 1 }}.{{ item }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <div class="chart-header">
          <h3>历史趋势分析</h3>
          <div class="chart-controls">
            <el-date-picker
              v-model="waterVal"
              type="datetimerange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm:ss"
              date-format="YYYY/MM/DD ddd"
              time-format="A hh:mm:ss"
              :disabled-date="disabledDate"
              @change="changeTime"
              class="customdatapicker"
              size="default"
            />
          </div>
        </div>
        <div class="chart-content">
          <PublicCharts :seriesData="seriesData" :xAxisData="xAxisData" />
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-header">
          <h3>24小时加药量监控</h3>
          <div class="time-badge">24h</div>
        </div>
        <div class="chart-content">
          <PublicCharts :seriesData="DosinseriesData" :xAxisData="DosinxAxisData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import TypewriterText from '@/components/TypewriterText/TypewriterText.vue'
import { queryequipmentLocationList, queryPointAnalysis, queryDosing } from './index.api'
import EmptyData from '@/components/emptyData/index.vue'
import emptyImagePath from '@/assets/images/noData.png'
import hand from '@/assets/images/hand3.png'
import waterMaintenanceImg from '@/assets/images/waterder.png'
import { formatDate } from '@/utils/formatTime'
import { getTabsList, getDataList, getechartsData } from '../pEcharts/index.api'
import emitter from '@/utils/eventBus.js'
import { useCache, CACHE_KEY, useLocalCache } from '@/hooks/web/useCache'
import PublicCharts from '@/components/publicCharts/indexCopy.vue'
import { el } from 'element-plus/es/locale'
import { log } from 'console'

// 缓存和初始数据
const { wsCache } = useLocalCache()
emitter.on('projectListChanged', (e) => {
  location.reload()
})

const waterVal = ref<[Date, Date]>([
  new Date(new Date().setDate(new Date().getDate() - 7)),
  new Date()
])

const disabledDate = (time: Date) => {
  const oneYearAgo = new Date()
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)
  return time.getTime() > Date.now() || time.getTime() < oneYearAgo.getTime()
}

const cachedProjects = wsCache.get(CACHE_KEY.projectList)

const waterMaintenanceData = ref<any[]>([])
const equipmentLocationData = ref<any[]>([])
const cilwaterValueData = ref<any>()
const xAxisData = ref<string[]>([])
const seriesData = ref<any[]>([])
const DosinxAxisData = ref<string[]>([])
const DosinseriesData = ref<any[]>([])

function transformHisData(his: any[], name: string, unit: string) {
  xAxisData.value = his.map((item) => item.time)
  const seriesName = `${name} ${unit ? unit : ''}`.trim()
  // const stackName = 'Total'
  seriesData.value = [
    {
      name: seriesName,
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.value)
    },
    {
      name: '环比',
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.hbValue)
    },
    {
      name: '同比',
      type: 'line',
      smooth: true,
      // stack: stackName, // 添加堆叠属性
      // areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.tbValue)
    }
  ]
}

const refreshData =() => {
  const params = {
    projectId: cachedProjects.id
  }
  queryequipmentLocationList(params).then((res) => {
    if (res.data.length > 0) {
      // 使用order排序
      equipmentLocationData.value = res.data.sort((a, b) => a.order - b.order)
    } else {
      equipmentLocationData.value = []
    }
  })
}
const getwaterMaintenanceData = () => {
  getTabsList(cachedProjects.id).then((res) => {
    if (res.data.length > 0) {
      // 筛选出model为TempHumidityWaterSensor的设备
      const tempHumidityWaterSensor = res.data.filter(
        (item) => item.model === 'TempHumidityWaterSensor'
      )
      tempHumidityWaterSensor.forEach((item) => {
        const params = {
          deviceId: item.deviceId,
          pointType: 0
        }
        getDataList(params).then((res) => {
          if (res.data.datas.length > 0) {
            waterMaintenanceData.value = res.data.datas
            const startTime = formatDate(waterVal.value[0])
            const endTime = formatDate(waterVal.value[1])
            getparms(waterMaintenanceData.value[0], startTime, endTime)
          } else {
            waterMaintenanceData.value = []
          }
        })
      })
    } else {
      waterMaintenanceData.value = []
    }
  })
  const params = {
    projectId: cachedProjects.id
  }
  queryequipmentLocationList(params).then((res) => {
    if (res.data.length > 0) {
      // 使用order排序
      equipmentLocationData.value = res.data.sort((a, b) => a.order - b.order)
      const PointParams = {
        projectId: cachedProjects.id,
        deviceId: equipmentLocationData.value[0].deviceId,
        identifier: equipmentLocationData.value[0].identifier,
        name: equipmentLocationData.value[0].name
      }
      // getDosingecharts(equipmentLocationData.value[0])
      changeDeviceInformation(equipmentLocationData.value[0], 0)
    } else {
      equipmentLocationData.value = []
    }
  })
}

const selectedIndex = ref(0)
// 点击点位数据获取参数
const getpointdata = (value: any) => {
  const params = {
    deviceId: value.deviceId,
    identifier: value.identifier,
    startTime: formatDate(waterVal.value[0]),
    endTime: formatDate(waterVal.value[1]),
    displayStats: true,
    displayGrowth: true
  }
  changewaterEch(params)
}

// 接收图表params数据
const getparms = (value: any, startTime: string, endTime: string) => {
  // 存储当前点位
  cilwaterValueData.value = value

  const params = {
    deviceId: cilwaterValueData.value.deviceId,
    identifier: cilwaterValueData.value.identifier,
    startTime: startTime,
    endTime: endTime,
    displayStats: true,
    displayGrowth: true
  }
  changewaterEch(params)
}

// 图表时间选择
const changeTime = (value: [Date, Date]) => {
  // 接收时间和当前选中的点位
  const params = {
    deviceId: cilwaterValueData.value.deviceId,
    identifier: cilwaterValueData.value.identifier,
    startTime: formatDate(value[0]),
    endTime: formatDate(value[1]),
    displayStats: true,
    displayGrowth: true
  }
  changewaterEch(params)
}

// 查看点位图表信息
const changewaterEch = (value: any) => {
  getechartsData(value).then((res) => {
    if (res.code !== 200) {
      return
    }
    if (!Array.isArray(res.data.his) || res.data.his.length === 0) {
      return
    }
    transformHisData(res.data.his, res.data.name, res.data.unit)
  })
}
function DosingetransformHisData(his: any[], name: string, unit: string) {
  DosinxAxisData.value = his.map((item) => {
  const match = item.time.match(/(\d{2}):(\d{2}):\d{2}$/)
  return match ? `${match[1]}:${match[2]}` : ''
})
  const seriesName = `${name} ${unit ? unit : ''}`.trim()
  const stackName = 'Total'
  DosinseriesData.value = [
    {
      name: seriesName,
      type: 'line',
      // smooth: true,
      // stack: stackName, // 添加堆叠属性
      areaStyle: {}, // 添加面积样式属性
      data: his.map((item) => item.value)
    },
    // {
    //   name: '环比',
    //   type: 'line',
    //   smooth: true,
    //   stack: stackName, // 添加堆叠属性
    //   areaStyle: {}, // 添加面积样式属性
    //   data: his.map((item) => item.hbValue)
    // },
    // {
    //   name: '同比',
    //   type: 'line',
    //   smooth: true,
    //   stack: stackName, // 添加堆叠属性
    //   areaStyle: {}, // 添加面积样式属性
    //   data: his.map((item) => item.tbValue)
    // }
  ]
}
const getDosingecharts =()=>{
  const params = {
    projectId: cachedProjects.id,
    identifier: 'scale_inhibitor_quantity',
  }
  queryDosing(params).then((res) => {
    // console.log(res,'---加药设备走势')
    if (res.code !== 200) {
     return
    }
    if (!Array.isArray(res.data.his) || res.data.his.length === 0) {
      return
    }
    DosingetransformHisData(res.data.his, res.data.name, res.data.unit)
  })
}

// 点击查看设备信息
const processCounter = ref(0) // 用于生成唯一的 id
const allProcessesDone = ref(false)
const changeDeviceInformation = async (value: any, index: number) => {
  selectedIndex.value = index // 更新选中的索引
  const PointParams = {
    projectId: cachedProjects.id,
    deviceId: value.deviceId,
    identifier: value.identifier,
    name: value.name
  }

  // 清空 displayedProcesses 和重置索引
  displayedProcesses.value = []
  currentProcessIndex.value = 0
  allProcessesDone.value = false
  await getPointAnalysis(PointParams)
  // await getDosingecharts(value)

  // 如果有新的 processList，添加第一个项到 displayedProcesses
  if (processList.value.length > 0) {
    displayedProcesses.value = [{
      ...processList.value[0],
      id: processCounter.value++ // 为每个项添加唯一的 id
    }]
  }
}

// 查询点位分析数据
const processList = ref<any[]>([])//处理过程
const conclusionList = ref<any[]>([])//结论
const suggestListList = ref<any[]>([])//建议
const relationList = ref<any[]>([])//依赖指标
const getPointAnalysis = async (value: any) => {
  try {
    const res = await queryPointAnalysis(value)
    if (res.code === 200) {
      processList.value = res.data.processList
      conclusionList.value = res.data.conclusionList
      suggestListList.value = res.data.suggestList
      relationList.value = res.data.relationList
      // console.log(suggestListList.value, 'suggestListList.value')
    } else {
      processList.value = []
      conclusionList.value = []
      suggestListList.value = []
      relationList.value = []
    }
  } catch (error) {
    console.error('获取点位分析数据失败:', error)
    processList.value = []
    conclusionList.value = []
    suggestListList.value = []
    relationList.value = []
  }
}


// 控制逐行逐字显示
const currentProcessIndex = ref(0) // 当前正在显示的processList索引
const displayedProcesses = ref<any[]>([]) // 已显示的processList项

const handleProcessDone = () => {
  if (currentProcessIndex.value < processList.value.length - 1) {
    currentProcessIndex.value += 1
    const nextProcess = processList.value[currentProcessIndex.value]
    displayedProcesses.value.push({
      ...nextProcess,
      id: processCounter.value++ // 为每个项添加唯一的 id
    })
  } else {
    // 所有分析过程完成
    allProcessesDone.value = true
  }
}



// 监听processList的变化
// watch(
//   () => processList.value,
//   (newList) => {
//     if (newList.length > 0) {
//       displayedProcesses.value = [newList[0]]
//       currentProcessIndex.value = 0
//     } else {
//       displayedProcesses.value = []
//     }
//   },
//   { immediate: true }
// )

// 自动点击第一个点位
// watch(
//   () => equipmentLocationData.value,
//   (newData) => {
//     if (newData.length > 0 && selectedIndex.value === 0) {
//       const firstItem = newData[0]
//       getpointdata(firstItem, 0) // 自动点击第一个 item
//     }
//   },
//   { immediate: true }
// )

onMounted(() => {
  getwaterMaintenanceData()
  getDosingecharts()
})
</script>

<style lang="scss">
// @import '@/assets/styles/ctable.scss';
/* 全局样式 */
@import '@/assets/styles/datapicker.scss';
/* 全局样式 */
</style>
<style scoped lang="scss">
.water-maintenance {
  background: linear-gradient(135deg, rgba(2, 28, 51, 0.95) 0%, rgba(15, 45, 75, 0.9) 100%);
  min-height: calc(100vh - 90px);
  padding: 20px;
  color: #ffffff;

  // 页面标题栏
  .page-header {
    margin-bottom: 24px;

    .header-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: linear-gradient(135deg, rgba(40, 138, 231, 0.2) 0%, rgba(44, 145, 238, 0.1) 100%);
      border-radius: 12px;
      border: 1px solid rgba(40, 138, 231, 0.3);
      backdrop-filter: blur(10px);

      h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header-actions {
        .el-button {
          background: linear-gradient(135deg, #288ae7 0%, #2c91ee 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(40, 138, 231, 0.3);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(40, 138, 231, 0.4);
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .left-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .right-section {
      width: 400px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }

  // 通用区块样式
  .section-header {
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #2c91ee;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 4px;
        height: 18px;
        background: linear-gradient(135deg, #288ae7 0%, #2c91ee 100%);
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }

  // 监测指标区域
  .metrics-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .metric-card {
      background: linear-gradient(135deg, rgba(40, 138, 231, 0.15) 0%, rgba(44, 145, 238, 0.1) 100%);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(40, 138, 231, 0.2);
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #288ae7 0%, #2c91ee 100%);
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(40, 138, 231, 0.3);
        border-color: rgba(40, 138, 231, 0.4);
      }

      .metric-header {
        margin-bottom: 12px;

        .metric-name {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
        }
      }

      .metric-content {
        display: flex;
        align-items: baseline;
        gap: 8px;

        .metric-value {
          font-size: 28px;
          font-weight: 700;
          color: #00a0e9;
          text-shadow: 0 2px 4px rgba(0, 160, 233, 0.3);
        }

        .metric-unit {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 400;
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      opacity: 0.6;
    }
  }

  // 设备状态区域
  .device-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    .device-content {
      display: flex;
      gap: 24px;

      .device-diagram {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          max-width: 100%;
          max-height: 300px;
          object-fit: contain;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }
      }

      .device-list {
        width: 200px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        max-height: 300px;
        overflow-y: auto;
        padding-right: 8px;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(40, 138, 231, 0.5);
          border-radius: 2px;

          &:hover {
            background: rgba(40, 138, 231, 0.7);
          }
        }
      }

      .device-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        .device-number {
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #2b4f63 0%, #3a5f73 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 14px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .device-name {
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s ease;
          flex: 1;
          text-align: center;

          &.device-online {
            background: linear-gradient(135deg, #0493d6 0%, #288ae7 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(4, 147, 214, 0.3);

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(4, 147, 214, 0.4);
            }
          }

          &.device-offline {
            background: linear-gradient(135deg, #f56c6c 0%, #e74c3c 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
            }
          }
        }

        .device-selected {
          img {
            width: 24px;
            height: 24px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
          }
        }
      }
    }
  }

  // 分析区域
  .analysis-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    flex: 1;

    .analysis-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
      height: 100%;

      .analysis-process {
        flex: 1;

        .process-list {
          max-height: 200px;
          overflow-y: auto;
          padding-right: 8px;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(40, 138, 231, 0.5);
            border-radius: 2px;
          }

          .process-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #288ae7;

            .process-number {
              color: #2c91ee;
              font-weight: 600;
              min-width: 20px;
            }

            .process-status {
              margin-left: 8px;
            }
          }
        }
      }

      .dependency-indicators {
        .indicator-list {
          max-height: 150px;
          overflow-y: auto;
          padding-right: 8px;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(40, 138, 231, 0.5);
            border-radius: 2px;
          }

          .indicator-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 6px;

            .indicator-number {
              color: #2c91ee;
              font-weight: 600;
              min-width: 20px;
            }

            .indicator-text {
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
            }
          }
        }
      }
    }

    .analysis-empty {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
      opacity: 0.6;

      img {
        max-width: 200px;
        max-height: 200px;
      }
    }
  }

  // 结论与建议区域
  .conclusion-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    .conclusion-content {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .conclusion-item,
      .suggestion-item {
        .conclusion-list,
        .suggestion-list {
          max-height: 150px;
          overflow-y: auto;
          padding-right: 8px;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(40, 138, 231, 0.5);
            border-radius: 2px;
          }

          .conclusion-text,
          .suggestion-text {
            padding: 10px 16px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.5;
            border-left: 3px solid #2c91ee;
          }
        }
      }
    }
  }

  // 图表区域
  .charts-section {
    display: flex;
    gap: 24px;

    .chart-container {
      flex: 1;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      border-radius: 16px;
      padding: 24px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c91ee;
        }

        .chart-controls {
          .el-date-editor {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;

            &:hover {
              border-color: rgba(40, 138, 231, 0.5);
            }

            &.is-active {
              border-color: #288ae7;
            }
          }
        }

        .time-badge {
          padding: 6px 16px;
          background: linear-gradient(135deg, #288ae7 0%, #2c91ee 100%);
          border-radius: 20px;
          color: white;
          font-size: 14px;
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(40, 138, 231, 0.3);
        }
      }

      .chart-content {
        height: 300px;
        border-radius: 12px;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.05);
      }
    }
  }
}
</style>
