<template>
  <div class="p-2">
    <!-- 日期范围选择 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div class="search">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          unlink-panels
          @change="onDateChange"
        />
      </div>
    </transition>

    <!-- 数据表格 -->
    <el-card shadow="never" class="mt-4">
      <template #header>
        <div class="card-header">
          <span>操作历史</span>
          <el-button type="primary" icon="Refresh" @click="handleRefresh" :loading="state.loading">
            刷新
          </el-button>
        </div>
      </template>
      <el-table :data="historyData" v-loading="state.loading" stripe style="width: 100%;">
        <el-table-column type="index" label="序号" width="90" align="center" />
        <el-table-column prop="updateTime" label="创建时间" />
        <el-table-column prop="title" label="方案名称" align="center" />
        <el-table-column label="最近方案" align="center" width="150">
          <template #default="{ row }">
            <span v-if="getLatestPlanTitle(row)" class="latest-plan">
              {{ getLatestPlanTitle(row) }}
            </span>
            <span v-else class="no-data">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="powerUnitNames" label="所属机组" align="center" />
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
          <template #default="{ row }">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="onRowClick(row)" />
            </el-tooltip>
            <el-tooltip content="下载运维日志" placement="top">
              <el-button link type="primary" icon="Download" @click="handleDownload(row.id)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="state.total > 0"
        v-model:page="state.page.pageNum"
        v-model:limit="state.page.pageSize"
        :total="state.total"
        layout="total, prev, pager, next, jumper"
        @pagination="getpagination"
      />
      <!-- 分页控件 -->
    </el-card>

    <!-- 历史详情模态框 -->
    <el-dialog v-model="dialogVisible" width="75%" title="历史详情">
      <div class="report" v-for="row in tableData" :key="row.id">
        <div class="table-wrapper">
          <el-table style="width: 100%" :data="generateTableData(row.report)" stripe :span-method="arraySpanMethod" border>
            <!-- 第一层表头：指标 -->
            <el-table-column prop="target" label="指标" align="center" width="150" fixed="left" />
            <el-table-column :label="`${row.title || '--'} ${row.updateTime || '--'}`" align="center">
              <el-table-column prop="symbol" label="符号" align="center" />
              <el-table-column prop="unit" label="单位" align="center" />
              <template v-for="(item, itemIndex) in row.report.items" :key="`item-${itemIndex}`">
                <el-table-column :label="item.title" align="center">
                  <template #header>
                    <span v-if="item.title">
                      <img
                        src="@/assets/icons/svg/best.svg"
                        alt="best"
                        style="width: 20px; height:20px; margin-right: 4px; vertical-align: middle;"
                      />
                      {{ item.title }}
                    </span>
                    <span v-else>--</span>
                  </template>
                  <template v-for="(member, memberIndex) in item.members" :key="`member-${memberIndex}`">
                    <el-table-column :label="member.current.powerUnitName" align="center">
                      <el-table-column :prop="`currentData_item${itemIndex}_member${memberIndex}`" label="当前值" align="center" />
                      <el-table-column :prop="`willData_item${itemIndex}_member${memberIndex}`" label="模拟值" align="center" />
                      <el-table-column :prop="`changeData_item${itemIndex}_member${memberIndex}`" label="变化值" align="center" />
                    </el-table-column>
                  </template>
                </el-table-column>
              </template>
            </el-table-column>
          </el-table>
          <!-- 动态建议 -->
          <div class="conclusion">
            <div></div>
            <div class="line">
              <p class="conclusion-title">最优推演结果</p>
              <div class="conclusion-content1">
                <span>&nbsp;&nbsp;方案：{{ row.report.bestPlanTitle !== null ? `${row.report.bestPlanTitle} ` : '--' }}</span>
                <span
                  >&nbsp;&nbsp;理论提升发电量：{{ row.report.improvePower !== null ? `${row.report.improvePower} kW·h` : '--&nbsp;&nbsp;kW·h' }}</span
                >
                <span>
                  &nbsp;&nbsp;理论综合提升发电量：{{ row.report.realImprovePower !== null ? `${row.report.realImprovePower}
                  kW·h` : '--&nbsp;&nbsp;kW·h' }}
                </span>
                <span> &nbsp;&nbsp;最优换算系数：{{ row.report.bestValue !== null ? `${row.report.bestValue}` : '--' }} </span>
                <span> &nbsp;&nbsp;当前换算系数：{{ row.report.currentBestValue !== null ? `${row.report.currentBestValue}` : '--' }} </span>
              </div>
            </div>
            <div class="line">
              <p class="conclusion-title">预检查</p>
              <div class="conclusion-content1">
                <ul v-infinite-scroll="load" class="infinite-list" style="overflow: auto">
                  <li v-for="(i,index2) in row.preCheckList" :key="i" class="infinite-list-item">{{index2+1}}·{{ i }}</li>
                </ul>
              </div>
            </div>
            <div class="line">
              <p class="conclusion-title">推演过程</p>
              <div class="conclusion-content1">
                <ul v-infinite-scroll="load" class="infinite-list" style="overflow: auto">
                  <li v-for="(i, index1) in row.processList" :key="i.id" class="infinite-list-item">
                    {{ index1 + 1 }}·{{ i.value }}
                    <!-- Popover 触发图标 -->
                    <el-popover trigger="click" placement="top" width="300">
                      <!-- Popover 内容 -->
                      <div v-html="formatExplain(i.explain)" />
                      <!-- Popover 触发元素 -->
                      <template #reference>
                        <el-icon :size="16" color="#ebe5b5" class="popover-icon">
                          <QuestionFilled />
                        </el-icon>
                      </template>
                    </el-popover>
                  </li>
                </ul>
              </div>
            </div>
            <div class="line">
              <p class="conclusion-title">建议</p>
              <div class="conclusion-content1">
                <div class="recommendation-row" v-for="(recommendation, index) in formatRecommendations(row.conclusion)" :key="index">
                  <el-tooltip class="item" placement="top" :disabled="true">
                    <div class="recommendation-item">&nbsp;&nbsp;{{ index + 1 }}·{{ recommendation }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
          <div class="alternative-solutions">
            <candidate :candidate-data="candidateData" :total="candidateTotal" :report-id="activeTab" @get-data="handleCandidateGetData"></candidate>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ComponentInternalInstance } from 'vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'
import { getHistoryList } from './index.api'
import { getSmartCandidateList, downloadReportLog } from '../analysisReport/index.api'
import emitter from '@/utils/eventBus.js'
import candidate from '../analysisReport/candidate/index.vue'
// 本地缓存中的项目信息
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)

// 日期范围
const dateRange = ref<(Date | string)[]>(['', ''])

// 组件状态
const state = reactive({
  page: {
    pageSize: 10,
    pageNum: 1,
  },
  total: 0,
  loading: false,
  query: {
    startTime: '',
    endTime: '',
  },
})

// 列表数据
const historyData = ref<any[]>([])

// 模态框数据
const dialogVisible = ref(false)
const selectedRow = ref<any>({})

// 添加候选方案相关的数据状态
const candidateData = ref<any[]>([])
const candidateTotal = ref(0)
const activeTab = ref<string>('')

// 候选方案分页和查询状态
const candidatePageNum = ref(1)
const candidatePageSize = ref(10)
const candidatePlanName = ref('')

// 候选方案查询表单
const candidateSearchForm = ref({
  planName: '',
})

// 候选方案分页状态
const candidateCurrentPage = ref(1)

// 候选方案表格数据处理
const candidateTableData = computed(() => {
  const result: any[] = []
  candidateData.value.forEach((plan) => {
    plan.items.forEach((item: any, itemIndex: number) => {
      result.push({
        id: `${plan.id}_${itemIndex}`,
        planId: plan.id,
        planName: plan.planName,
        powerUnitName: item.will.powerUnitName,
        exhaustTemperature: item.will.exhaustTemperature,
        unitPressure: item.will.unitPressure,
        inTemp: item.will.inTemp,
        coolApproach: item.will.coolApproach,
        temperatureRise: item.will.temperatureRise,
        ttd: item.will.ttd,
        vacuumValue: item.will.vacuumValue,
        improveEfficiency: item.improveEfficiency,
        improvePower: item.improvePower,
        plusConsumption: plan.plusConsumption,
        realImprovePower: plan.realImprovePower,
        bestValue: plan.bestValue,
        itemIndex: itemIndex,
        totalItems: plan.items.length,
      })
    })
  })
  return result
})

// 候选方案查询
const handleCandidateSearch = () => {
  candidateCurrentPage.value = 1
  getCandidateData(activeTab.value, 1, candidatePageSize.value, candidateSearchForm.value.planName)
}

// 候选方案重置
const handleCandidateReset = () => {
  candidateSearchForm.value = {
    planName: '',
  }
  candidateCurrentPage.value = 1
  getCandidateData(activeTab.value, 1, candidatePageSize.value)
}

// 候选方案页面大小改变
const handleCandidateSizeChange = (val: number) => {
  candidatePageSize.value = val
  candidateCurrentPage.value = 1
  getCandidateData(activeTab.value, 1, val, candidateSearchForm.value.planName)
}

// 候选方案当前页改变
const handleCandidateCurrentChange = (val: number) => {
  candidateCurrentPage.value = val
  getCandidateData(activeTab.value, val, candidatePageSize.value, candidateSearchForm.value.planName)
}

// 候选方案单元格合并逻辑
const candidateSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 需要合并的列：方案名称、增加常用电量、理论综合提升发电量、换算系数
  const mergeColumns = ['planName', 'plusConsumption', 'realImprovePower', 'bestValue']

  if (mergeColumns.includes(column.property)) {
    if (row.itemIndex === 0) {
      return {
        rowspan: row.totalItems,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
}

// 格式化日期工具
const formatDate = (date: Date | string): string => {
  if (date instanceof Date) {
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    return `${y}-${m}-${d}`
  }
  // 如果已经是字符串，则直接返回
  return date
}

// 请求函数
const getHistoryListData = () => {
  const projectId = cachedProjects.id
  const params = {
    ...state.page,
    ...state.query,
    projectId,
    analysisType: 0,
  }
  state.loading = true
  getHistoryList(params)
    .then((res) => {
      const rows = res.data.rows || []
      historyData.value = rows
      state.total = res.data.total || rows.length
    })
    .finally(() => {
      state.loading = false
    })
}

// 日期选择变化
const onDateChange = (val: (Date | string)[]) => {
  if (val && val.length === 2) {
    const start = formatDate(val[0])
    const end = formatDate(val[1])
    state.query.startTime = `${start} 00:00:00`
    state.query.endTime = `${end} 23:59:59`
    state.page.pageNum = 1
    getHistoryListData()
  }
}

const getpagination = () => {
  getHistoryListData()
}

const onRowClick = (row: any) => {
  // 1. 解析 report 字符串
  const parsedReport = row.report ? (typeof row.report === 'string' ? JSON.parse(row.report) : row.report) : null
  // 2. 构造一个 TableRow
  const detailRow: TableRow = {
    id: row.id,
    title: row.title,
    updateTime: row.updateTime,
    report: parsedReport,
    preCheckList: row.preCheckList || [],
    processList: row.processList || [],
    conclusion: row.conclusion,
  }

  // 3. 覆盖原来的 tableData
  tableData.value = [detailRow]

  // 4. 设置activeTab
  activeTab.value = String(row.id)

  // 5. 获取候选方案数据
  getCandidateData(String(row.id), 1, 10)

  // 6. 显示弹窗
  dialogVisible.value = true
}

// 获取候选方案数据
const getCandidateData = (reportId: string, pageNum = 1, pageSize = 10, planName?: string) => {
  const params = {
    reportId: reportId,
    pageNum: pageNum,
    pageSize: pageSize,
    ...(planName && { planName }),
  }

  getSmartCandidateList(params)
    .then((res) => {
      candidateData.value = Array.isArray(res.data.rows) ? res.data.rows : []
      candidateTotal.value = res.data.total || 0
    })
    .catch((error) => {
      console.error('获取候选方案数据失败:', error)
      candidateData.value = []
      candidateTotal.value = 0
    })
}

// 处理候选方案数据获取
const handleCandidateGetData = (params: { reportId: string; pageNum: number; pageSize: number; planName?: string }) => {
  getCandidateData(params.reportId, params.pageNum, params.pageSize, params.planName)
}

// 获取最近方案标题
const getLatestPlanTitle = (row: any) => {
  try {
    // 解析 report 字符串
    const report = row.report ? (typeof row.report === 'string' ? JSON.parse(row.report) : row.report) : null

    if (!report || !report.items || !Array.isArray(report.items)) {
      return null
    }

    // 查找第一个有 title 的 item
    for (const item of report.items) {
      if (item && item.title && item.title.trim() !== '') {
        return item.title
      }
    }

    return null
  } catch (error) {
    console.error('解析最近方案数据失败:', error)
    return null
  }
}

// 刷新数据
const handleRefresh = () => {
  ElMessage.success('正在刷新数据...')
  getHistoryListData()
}

// 下载运维日志
const handleDownload = async (reportId: string | number) => {
  if (!reportId) {
    ElMessage.error('请先选择一个报告')
    return
  }
  try {
    const res = await downloadReportLog(String(reportId))
    // 获取实际的Blob数据
    let blob = res.data || res
    // 检查是否是Blob对象
    if (blob instanceof Blob) {
      // 将Blob转换为文本
      const text = await blob.text()
      let finalJsonString: string
      try {
        const jsonData = JSON.parse(text)
        finalJsonString = JSON.stringify(jsonData, null, 2)
      } catch (e) {
        finalJsonString = text
      }
      // 创建新的Blob用于下载
      const downloadBlob = new Blob([finalJsonString], { type: 'application/json;charset=utf-8' })
      const url = window.URL.createObjectURL(downloadBlob)
      const link = document.createElement('a')
      link.href = url

      // 设置下载文件名
      const fileName = `运维日志_${reportId}_${new Date().toISOString().slice(0, 10)}.json`
      link.download = fileName

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理资源
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('运维日志下载成功')
    } else {
      // 如果不是Blob，按原来的方法处理
      const jsonString = JSON.stringify(blob, null, 2)
      const downloadBlob = new Blob([jsonString], { type: 'application/json;charset=utf-8' })
      const url = window.URL.createObjectURL(downloadBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `运维日志_${reportId}_${new Date().toISOString().slice(0, 10)}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      ElMessage.success('运维日志下载成功')
    }
  } catch (error) {
    ElMessage.error('运维日志下载失败')
  }
}

const mergeTargets = ['增加常用电量', '理论综合提升发电量', '最优换算系数']
// 定义 TableRow 接口
interface TableRow {
  id: number
  title: string | null
  updateTime: string | null
  report: {
    bestPlanTitle: string | null
    improvePower: number | null
    increaseCommonPower: number | null
    realImprovePower: number | null
    bestValue: number | null
    currentBestValue: number | null
    electricityChangeRate: any
    items: Array<{
      title: string | null
      improvePower: number
      increaseCommonPower: number
      realImprovePower: number
      bestValue: number
      electricityChangeRate: any
      members: Array<{
        current: any
        will: any
        improveUnitPressure: number | null
        improveEfficiency: number | null
        improvePower: number | null
      }>
    }>
  }
  preCheckList: string[]
  processList: Array<{ id: number; value: string; explain?: string }>
  conclusion: string
}

// 更新 tableData 的类型
const tableData = ref<TableRow[]>([])

// 静态内容
const staticData = [
  { target: '真空', symbol: '/', unit: 'kPa' },
  { target: '机组排汽温度', symbol: 'ts', unit: '℃' },
  { target: '对应背压值', symbol: 'BP', unit: 'kPa' },
  { target: '冷却水进口温度', symbol: 't1', unit: '℃' },
  { target: '冷却水出口温度', symbol: 't2', unit: '℃' },
  { target: '逼近度', symbol: 'ap', unit: '℃' },
  { target: '冷却水温升', symbol: 'Δt', unit: '℃' },
  { target: '端差', symbol: 'δt', unit: '℃' },
  { target: '提升发电效率', symbol: 'ΔP', unit: '%' },
  { target: '理论提升发电量', symbol: 'ΔPW', unit: 'kW·h' },
  { target: '增加常用电量', symbol: 'ΔPW', unit: 'kW·h' },
  { target: '理论综合提升发电量', symbol: 'ΔPW', unit: 'kW·h' },
  { target: '最优换算系数', symbol: '/', unit: '/' },
]

// 格式化 explain 字段
const formatExplain = (explain: string) => {
  if (!explain) return ''
  return explain.replace(/\n/g, '<br/>')
}

const load = () => {}

// 监听项目列表变化
emitter.on('projectListChanged', () => {
  location.reload()
})

// 格式化建议
const formatRecommendations = (conclusion: string) => {
  try {
    return JSON.parse(conclusion)
  } catch (e) {
    return []
  }
}

// 生成表格数据
const generateTableData = (report: TableRow['report']) => {
  if (!report || !Array.isArray(report.items)) {
    return [] // 如果 report 或 report.items 为 null，则返回空数组
  }

  const tableRows = staticData
    .filter((data) => !['总的提升发电量', '总增加常用电量'].includes(data.target))
    .map((data) => {
      const row: Record<string, any> = {
        target: data.target,
        symbol: data.symbol,
        unit: data.unit,
        mergeInfo: [], // 存储需要合并的信息
      }

      report.items.forEach((item, itemIndex) => {
        if (Array.isArray(item.members) && item.members.length > 0) {
          const colspan = item.members.length * 3 // 每个 member 对应三个子列
          row.mergeInfo.push({ itemIndex, colspan })

          item.members.forEach((member, memberIndex) => {
            const baseKey = `item${itemIndex}_member${memberIndex}`

            if (mergeTargets.includes(data.target)) {
              // 对于需要合并的指标，从 item 中提取数据
              row[`currentData_item${itemIndex}_member${memberIndex}`] = getCurrentData(data.target, member.current, item)
              row[`willData_item${itemIndex}_member${memberIndex}`] = getWillData(data.target, member.will, item)
              row[`changeData_item${itemIndex}_member${memberIndex}`] = getChangeData(data.target, member, item)
            } else {
              // 对于其他指标，从 member 中提取数据
              row[`currentData_item${itemIndex}_member${memberIndex}`] = getCurrentData(data.target, member.current, null)
              row[`willData_item${itemIndex}_member${memberIndex}`] = getWillData(data.target, member.will, null)
              row[`changeData_item${itemIndex}_member${memberIndex}`] = getChangeData(data.target, member, null)
            }
          })
        }
      })

      // 仅对需要合并的指标计算 colspan
      if (mergeTargets.includes(data.target)) {
        const totalMembers = report.items.reduce((sum, item) => sum + (Array.isArray(item.members) ? item.members.length : 0), 0)
        row.colspan = totalMembers * 3 // 每个 member 对应三个子列
      }

      return row
    })

  // 过滤掉为 null 的行
  const filteredRows = tableRows.filter((row) => row !== null)

  // 标记需要隐藏的 'target' 单元格
  const mergedRowCount: Record<string, number> = {}
  mergeTargets.forEach((target) => {
    mergedRowCount[target] = 0
  })

  return filteredRows.map((row) => {
    if (mergeTargets.includes(row.target)) {
      if (mergedRowCount[row.target] === 0) {
        mergedRowCount[row.target]++
        return row
      } else {
        return { ...row, target: '' }
      }
    }
    return row
  })
}

// 获取当前值
const getCurrentData = (target: string, current: any, item: any): string => {
  if (mergeTargets.includes(target)) {
    if (!item) return '/'
    switch (target) {
      case '增加常用电量':
        return typeof item.increaseCommonPower === 'number' ? item.increaseCommonPower.toFixed(2) : '/'
      case '理论综合提升发电量':
        return typeof item.realImprovePower === 'number' ? item.realImprovePower.toFixed(2) : '/'
      case '最优换算系数':
        return typeof item.bestValue === 'number' ? item.bestValue.toFixed(2) : '/'
      case '当前换算系数':
        return typeof item.currentBestValue === 'number' ? item.currentBestValue.toFixed(2) : '/'

      default:
        return '/'
    }
  } else {
    if (!current) return '/'
    switch (target) {
      case '真空':
        return current.vacuumValue ?? '/'
      case '机组排汽温度':
        return current.exhaustTemperature ?? '/'
      case '对应背压值':
        return current.unitPressure ?? '/'
      case '冷却水进口温度':
        return current.inTemp ?? '/'
      case '冷却水出口温度':
        return current.outTemp ?? '/'
      case '逼近度':
        return current.coolApproach ?? '/'
      case '冷却水温升':
        return current.temperatureRise ?? '/'
      case '端差':
        return current.ttd ?? '/'
      case '提升发电效率':
        return typeof current.improveEfficiency === 'number' ? current.improveEfficiency.toFixed(2) : '/'
      case '理论提升发电量':
        return typeof current.improvePower === 'number' ? current.improvePower.toFixed(2) : '/'
      default:
        return '/'
    }
  }
}
// console.log();

// 获取模拟值
const getWillData = (target: string, will: any, item: any): string => {
  if (mergeTargets.includes(target)) {
    if (!item) return '/'
    switch (target) {
      case '增加常用电量':
        return typeof item.realImprovePower === 'number' ? item.realImprovePower.toFixed(2) : '/'
      case '理论综合提升发电量':
        return '/'
      case '最优换算系数':
        return '/'
      default:
        return '/'
    }
  } else {
    if (!will) return '/'
    switch (target) {
      case '真空':
        return will.vacuumValue ?? '/'
      case '机组排汽温度':
        return will.exhaustTemperature ?? '/'
      case '对应背压值':
        return will.unitPressure ?? '/'
      case '冷却水进口温度':
        return will.inTemp ?? '/'
      case '冷却水出口温度':
        return will.outTemp ?? '/'
      case '逼近度':
        return will.coolApproach ?? '/'
      case '冷却水温升':
        return will.temperatureRise ?? '/'
      case '端差':
        return will.ttd ?? '/'
      case '提升发电效率':
        return typeof will.improveEfficiency === 'number' ? will.improveEfficiency.toFixed(2) : '/'
      case '理论提升发电量':
        return typeof will.improvePower === 'number' ? will.improvePower.toFixed(2) : '/'
      default:
        return '/'
    }
  }
}

// 获取变化值
const getChangeData = (target: string, member: any, item: any): string => {
  if (mergeTargets.includes(target)) {
    if (!item) return '/'
    switch (target) {
      case '增加常用电量':
        return typeof item.bestValue === 'number' ? item.bestValue.toFixed(2) : '/'
      case '理论综合提升发电量':
        return '/'
      case '最优换算系数':
        return '/'
      default:
        if (!member || !member.current || !member.will) return '/'
        switch (target) {
          case '真空':
            return typeof member.will.vacuumValue === 'number' && typeof member.current.vacuumValue === 'number'
              ? (member.will.vacuumValue - member.current.vacuumValue).toFixed(2)
              : '/'
          case '机组排汽温度':
            return typeof member.will.exhaustTemperature === 'number' && typeof member.current.exhaustTemperature === 'number'
              ? (member.will.exhaustTemperature - member.current.exhaustTemperature).toFixed(2)
              : '/'
          case '对应背压值':
            return typeof member.will.unitPressure === 'number' && typeof member.current.unitPressure === 'number'
              ? (member.will.unitPressure - member.current.unitPressure).toFixed(2)
              : '/'
          case '冷却水进口温度':
            return typeof member.will.inTemp === 'number' && typeof member.current.inTemp === 'number'
              ? (member.will.inTemp - member.current.inTemp).toFixed(2)
              : '/'
          case '冷却水出口温度':
            return typeof member.will.outTemp === 'number' && typeof member.current.outTemp === 'number'
              ? (member.will.outTemp - member.current.outTemp).toFixed(2)
              : '/'
          case '逼近度':
            return typeof member.will.coolApproach === 'number' && typeof member.current.coolApproach === 'number'
              ? (member.will.coolApproach - member.current.coolApproach).toFixed(2)
              : '/'
          case '冷却水温升':
            return typeof member.will.temperatureRise === 'number' && typeof member.current.temperatureRise === 'number'
              ? (member.will.temperatureRise - member.current.temperatureRise).toFixed(2)
              : '/'
          case '端差':
            return typeof member.will.ttd === 'number' && typeof member.current.ttd === 'number'
              ? (member.will.ttd - member.current.ttd).toFixed(2)
              : '/'
          case '提升发电效率':
            return typeof member.improveEfficiency === 'number' ? member.improveEfficiency.toFixed(2) : '/'
          case '理论提升发电量':
            return typeof member.improvePower === 'number' ? member.improvePower.toFixed(2) : '/'
          default:
            return '/'
        }
    }
  } else {
    if (!member || !member.current || !member.will) return '/'
    switch (target) {
      case '真空':
        return typeof member.will.vacuumValue === 'number' && typeof member.current.vacuumValue === 'number'
          ? (member.will.vacuumValue - member.current.vacuumValue).toFixed(2)
          : '/'
      case '机组排汽温度':
        return typeof member.will.exhaustTemperature === 'number' && typeof member.current.exhaustTemperature === 'number'
          ? (member.will.exhaustTemperature - member.current.exhaustTemperature).toFixed(2)
          : '/'
      case '对应背压值':
        return typeof member.will.unitPressure === 'number' && typeof member.current.unitPressure === 'number'
          ? (member.will.unitPressure - member.current.unitPressure).toFixed(2)
          : '/'
      case '冷却水进口温度':
        return typeof member.will.inTemp === 'number' && typeof member.current.inTemp === 'number'
          ? (member.will.inTemp - member.current.inTemp).toFixed(2)
          : '/'
      case '冷却水出口温度':
        return typeof member.will.outTemp === 'number' && typeof member.current.outTemp === 'number'
          ? (member.will.outTemp - member.current.outTemp).toFixed(2)
          : '/'
      case '逼近度':
        return typeof member.will.coolApproach === 'number' && typeof member.current.coolApproach === 'number'
          ? (member.will.coolApproach - member.current.coolApproach).toFixed(2)
          : '/'
      case '冷却水温升':
        return typeof member.will.temperatureRise === 'number' && typeof member.current.temperatureRise === 'number'
          ? (member.will.temperatureRise - member.current.temperatureRise).toFixed(2)
          : '/'
      case '端差':
        return typeof member.will.ttd === 'number' && typeof member.current.ttd === 'number' ? (member.will.ttd - member.current.ttd).toFixed(2) : '/'
      case '提升发电效率':
        return typeof member.improveEfficiency === 'number' ? member.improveEfficiency.toFixed(2) : '/'
      case '理论提升发电量':
        return typeof member.improvePower === 'number' ? member.improvePower.toFixed(2) : '/'
      default:
        return '/'
    }
  }
}

// 实现 span-method
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  let rowspan = 1
  let colspan = 1

  // 定义静态列的数量
  const staticColumnCount = 3 // target, symbol, unit

  if (mergeTargets.includes(row.target)) {
    // 遍历 mergeInfo 以找到当前列是否需要合并
    let currentCol = staticColumnCount
    for (const merge of row.mergeInfo) {
      const { itemIndex, colspan: itemColspan } = merge
      if (columnIndex === currentCol) {
        // 当前列是需要合并的起始列
        colspan = itemColspan
        break
      } else if (columnIndex > currentCol && columnIndex < currentCol + itemColspan) {
        // 当前列在合并范围内，隐藏该单元格
        rowspan = 0
        colspan = 0
        break
      }
      currentCol += itemColspan
    }
  }

  return { rowspan, colspan }
}
// 初始加载
onMounted(() => {
  // 默认今天
  const todayStr = new Date().toISOString().slice(0, 10)
  dateRange.value = [todayStr, todayStr]
  // 手动调用变化方法，内部会格式化
  onDateChange(dateRange.value)
})
</script>

<style scoped lang="scss">
:deep(.el-tag.el-tag--info) {
  color: #000 !important;
}

:deep(.el-card) {
  background: rgba(2, 28, 51, 0.5);
  // box-shadow: inset 0px 2px 28px rgba(33, 148, 255, 0.5);
  border: none;
}

:deep(.el-card__body) {
  border: none;
}

:deep(.el-table, .el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table tr) {
  border: none;
  background-color: transparent;
}

:deep(.el-table th) {
  background-color: rgba(7, 53, 92, 1);
  color: rgba(204, 204, 204, 1) !important;
  font-size: 14px;
  font-weight: 400;
}

:deep(.el-table) {
  --el-table-border-color: none;
}

/*选中边框 */
:deep(.el-table__body-wrapper .el-table__row:hover) {
  background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  outline: 2px solid rgba(19, 89, 158, 1);
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row:hover td) {
  background: none !important;
}

:deep(.el-table__header thead tr th) {
  background: rgba(7, 53, 92, 1) !important;
  color: #ffffff;
}

:deep(.el-table_1_column_1 .is-leaf .el-table__cell) {
  color: #fff;
}

:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #07355c;
}

:deep(.el-tree-node__expand-icon) {
  color: #fff;
}

:deep(.el-select__wrapper) {
  width: 180px;
  color: #fff !important;
  background: rgb(3, 43, 82) !important;
  box-shadow: 0 0 0 0px #034374 inset !important;
  border: 1px solid #034374 !important;
}

:deep(.el-select__placeholder) {
  color: #fff;
}

:deep(.el-tree-node__label) {
  color: #fff;
}

:deep(.el-tree-node__content) {
  &:hover {
    background: linear-gradient(90deg, rgba(50, 90, 125, 0) 0%, rgba(50, 90, 124, 1) 47.92%, rgba(50, 90, 124, 0) 100%) !important;
  }
}

:deep(.el-select__tags .el-tag--info) {
  background-color: #153059 !important;
}
.text-left {
  display: flex;
  justify-content: flex-end;
}
.report-container {
  overflow-x: auto;
}
.report {
  background: #021c33;
  // box-shadow: inset 0px 2px 28px #2194ff;
  margin-bottom: 20px;
  // padding: 20px;
}

:deep(.el-table__body-wrapper tr td.el-table-fixed-column--left) {
  background-color: rgba(7, 53, 92, 1) !important;
}
:deep(.el-table__body-wrapper tr:hover td.el-table-fixed-column--left) {
  background-color: rgba(7, 53, 92, 1) !important;
}
:deep(.el-table__header-wrapper th.el-table-fixed-column--left) {
  background-color: rgba(7, 53, 92, 1) !important;
}
:deep(.el-table__row > :nth-child(1) > .cell) {
  color: #b08805;
}
:deep(.el-table th, .el-table tr, .el-table td) {
  background-color: #07355c;
  color: #fff;
  font-size: 18px;
  height: 5px;
  font-weight: Normal;
}

:deep(.el-table__header-wrapper) {
}
:deep(.el-table__header-wrapper th) {
  border: 1px solid #00429c;
  background-color: rgba(7, 53, 92, 1);
  color: #b08805;
}

:deep(.el-table__body-wrapper td) {
  border-top: none !important;
  border-bottom: none !important;
  border-left: 1px solid #00429c !important;
  border-right: 1px solid #00429c !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: #07355c !important;
}

:deep(.el-table__cell) {
  color: #fff;
}

:deep(.el-table__body-wrapper .el-table__row td) {
  background-color: inherit !important;
}
:deep(.el-date-editor .el-range-input) {
  color: #fff;
}
.conclusion {
  // margin: 10px 20px 0 20px;
  display: flex;
  justify-content: space-between;
}

.line {
  width: 25%;
  height: 250px;
  display: flex;
  flex-direction: column;
  border: 10px solid #021c33;
  border-image: url(@/assets/images/line.png) 10 round;

  .conclusion-title {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    vertical-align: top;
  }

  .conclusion-content {
    font-size: 16px;
    font-weight: 400;
    letter-spacing: 0px;
    color: rgba(255, 255, 255, 1);
    margin: 50px 0 0 30px;
    text-align: left;
    vertical-align: top;
  }

  .conclusion-content1 {
    height: 100%;
    overflow-y: auto;
    // margin: 30px 30px 0 30px;
    color: rgba(255, 255, 255, 1);
    span {
      display: block;
    }
    .infinite-list {
      list-style: none;
      margin: 0;
      padding: 0 0 0 5px;
    }

    &::-webkit-scrollbar-track {
      border-radius: 5px;
      background: #07355c;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgb(52, 86, 113);
      border-radius: 20px !important;
      border: 2px solid #07355c;
    }

    /* 针对 Firefox 浏览器 */
    scrollbar-width: thin;
    scrollbar-color: rgb(52, 86, 113) #07355c;
  }

  .recommendation-row {
    margin-bottom: 10px;
  }

  .recommendation-item {
    white-space: normal;
    word-break: break-word;
    cursor: pointer;
  }
}

/* 候选方案样式 */
.candidate {
  padding: 5px;
}

.search-form {
  margin-bottom: 5px;
  background-color: rgba(7, 53, 92, 0.5);
  border-radius: 4px;
}

.search-form :deep(.el-form-item__label) {
  color: #ffffff;
}

.search-form :deep(.el-input__inner) {
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.search-form :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.pagination-wrapper :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: rgba(7, 53, 92, 0.8);
  --el-pagination-text-color: #ffffff;
  --el-pagination-border-radius: 4px;
  --el-pagination-button-color: #ffffff;
  --el-pagination-button-bg-color: rgba(255, 255, 255, 0.1);
  --el-pagination-button-disabled-color: rgba(255, 255, 255, 0.3);
  --el-pagination-button-disabled-bg-color: rgba(255, 255, 255, 0.05);
  --el-pagination-hover-color: #409eff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

.card-header :deep(.el-button) {
  background: linear-gradient(135deg, #409eff 0%, #2e7ce0 100%);
  border: none;
  color: #ffffff;
  font-weight: 500;
  transition: all 0.3s ease;
}

.card-header :deep(.el-button:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}
</style>
